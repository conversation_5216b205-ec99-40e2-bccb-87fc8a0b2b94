# 《西游大战僵尸2》外挂调试测试脚本

## 🔍 问题分析

根据深度分析，发现以下关键问题：

### 1. **类加载时机问题**
- 游戏SWF可能需要完全加载后才能获取类定义
- 需要延迟初始化游戏类引用

### 2. **属性访问路径错误**
- PlayerVO的属性可能被加密或使用了不同的访问方式
- 需要通过_antiwear对象访问某些属性

### 3. **缺少详细调试信息**
- 原版代码没有足够的错误追踪
- 无法准确定位失败原因

## 🛠️ 解决方案

### 增强版MainTimeline.as特性：

1. **详细调试日志系统**
   - 每个步骤都有详细的日志输出
   - 错误堆栈追踪
   - 数据结构分析

2. **多重容错机制**
   - 多种属性访问方式
   - 游戏类加载检测
   - 延迟初始化

3. **玩家数据结构分析**
   - 自动分析玩家对象结构
   - 检测可用属性
   - 智能属性访问

## 📋 测试步骤

### 第一步：基础连接测试
1. 运行shell.swf
2. 运行localcon_T.swf
3. 查看控制台输出，确认：
   - `[DEBUG] MainTimeline 构造函数执行完成`
   - `[DEBUG] 游戏SWF加载完成，开始初始化`
   - `[DEBUG] 游戏类初始化完成，准备连接LocalConnection`
   - `[DEBUG] LocalConnection连接成功`

### 第二步：类加载测试
尝试执行任意功能，查看：
- `[DEBUG] 开始初始化游戏类引用`
- `[DEBUG] GamingUI类加载成功`
- `[DEBUG] XMLSingle类加载成功`
- `[DEBUG] MyFunction类加载成功`

### 第三步：玩家对象获取测试
选择玩家P1，执行修改金币功能，查看：
- `[DEBUG] 尝试获取玩家对象，玩家编号: 1`
- `[DEBUG] GamingUI实例获取成功`
- `[DEBUG] 玩家对象获取成功`

### 第四步：数据结构分析
查看玩家数据结构分析输出：
```
[DEBUG] === 开始分析玩家数据结构 ===
[DEBUG] playerVO存在
[DEBUG] playerVO.money = [值] (类型: [类型])
[DEBUG] playerVO.level = [值] (类型: [类型])
[DEBUG] playerVO._antiwear存在
[DEBUG] playerVO._antiwear.baseBloodVolume = [值]
```

### 第五步：功能执行测试
测试修改金币功能：
- 输入玩家：P1
- 功能：修改金币
- 数量：999999

查看输出：
- `[DEBUG] 开始修改金币，目标值: 999999`
- `[DEBUG] 原始金币值: [原值]`
- `[DEBUG] 金币修改成功！`

## 🚨 常见错误及解决方案

### 错误1：游戏类加载失败
```
[DEBUG] 错误：无法获取应用程序域
```
**解决方案**：等待游戏完全加载，或重新启动外挂

### 错误2：玩家对象获取失败
```
[DEBUG] 错误：玩家对象为null
```
**解决方案**：确保游戏中有玩家角色，或尝试不同的玩家编号

### 错误3：属性访问失败
```
[DEBUG] 直接赋值失败: [错误信息]
```
**解决方案**：查看是否通过_antiwear成功修改

## 📊 调试信息解读

### 成功的日志模式：
```
[DEBUG] === 开始执行外挂功能 ===
[DEBUG] 玩家编号: 1
[DEBUG] 功能类型: 13
[DEBUG] 所有游戏类初始化成功
[DEBUG] 玩家对象获取成功
[DEBUG] playerVO存在
[DEBUG] 原始金币值: 1000
[DEBUG] 直接赋值成功
[DEBUG] 修改后金币值: 999999
[DEBUG] 金币修改成功！
[DEBUG] 刷新UI完成
[DEBUG] === 外挂功能执行结束 ===
```

### 失败的日志模式：
```
[DEBUG] === 开始执行外挂功能 ===
[DEBUG] 错误：游戏尚未加载完成
```
或
```
[DEBUG] 错误：GamingUI类未初始化
```

## 🔧 进一步调试建议

如果基础功能仍然失败，可以：

1. **检查游戏版本兼容性**
2. **尝试不同的属性访问路径**
3. **使用Flash调试器查看运行时错误**
4. **检查LocalConnection权限设置**

## 📝 测试记录模板

```
测试时间：[时间]
游戏版本：[版本]
测试功能：[功能名称]
测试结果：[成功/失败]
错误信息：[具体错误]
解决方案：[采取的措施]
```
