# 《西游大战僵尸2》装备ID测试列表

## 🧪 推荐测试装备ID

### 基础武器类 (10101xxx)
```
10101001 - 金箍棒
10101002 - 金箍棒+1
10101003 - 金箍棒+2
10101004 - 金箍棒+3
10101005 - 金箍棒+4
10101006 - 金箍棒+5
10101007 - 金箍棒+6
10101008 - 金箍棒+7
10101009 - 金箍棒+8
10101010 - 金箍棒+9
```

### 基础防具类 (10201xxx)
```
10201001 - 布衣
10201002 - 布衣+1
10201003 - 布衣+2
10201004 - 布衣+3
10201005 - 布衣+4
```

### 基础饰品类 (10301xxx)
```
10301001 - 铜戒指
10301002 - 铜戒指+1
10301003 - 铜戒指+2
```

### 消耗品类 (20xxx)
```
20001 - 小血瓶
20002 - 中血瓶
20003 - 大血瓶
20011 - 小蓝瓶
20012 - 中蓝瓶
20013 - 大蓝瓶
```

### 材料类 (30xxx)
```
30001 - 铁矿石
30002 - 铜矿石
30003 - 银矿石
30004 - 金矿石
```

## 🔧 测试步骤

### 第一步：基础装备测试
1. **测试装备ID**: `10101001` (金箍棒)
2. **玩家**: P1
3. **功能**: 添加装备 (第1项)
4. **数量**: 1

**预期结果**：
```
[DEBUG] 开始添加装备功能，ID: 10101001, 数量: 1
[DEBUG] 装备ID 10101001 有效，名称: 金箍棒
[DEBUG] 创建第 1 个装备
[DEBUG] 装备创建成功，类型: object
[DEBUG] 装备名称: 金箍棒
[DEBUG] 装备等级: 1
[DEBUG] 通过MyFunction添加装备结果: true
[DEBUG] 第 1 个装备添加成功
[DEBUG] 装备添加完成，成功: 1/1
[DEBUG] 刷新UI完成
```

### 第二步：高级装备测试
1. **测试装备ID**: `10101010` (金箍棒+9)
2. **玩家**: P1
3. **功能**: 添加装备 (第1项)
4. **数量**: 1

### 第三步：消耗品测试
1. **测试装备ID**: `20001` (小血瓶)
2. **玩家**: P1
3. **功能**: 添加装备 (第1项)
4. **数量**: 10

### 第四步：批量添加测试
1. **测试装备ID**: `10101001` (金箍棒)
2. **玩家**: P1
3. **功能**: 添加装备 (第1项)
4. **数量**: 5

## ❌ 错误诊断

### 错误1：装备ID无效
```
[DEBUG] 装备ID 99999999 无效，在XML中未找到
```
**解决方案**：使用上面列表中的有效装备ID

### 错误2：装备创建失败
```
[DEBUG] 创建装备失败: Error #1069
```
**可能原因**：
1. 方法名错误
2. 参数类型错误
3. XML数据损坏

### 错误3：背包已满
```
[DEBUG] 背包已满，无法添加装备
```
**解决方案**：
1. 清理背包
2. 减少添加数量
3. 检查背包容量

### 错误4：MyFunction方法失败
```
[DEBUG] 通过MyFunction添加装备结果: false
```
**解决方案**：
1. 检查背包空间
2. 使用直接添加方法
3. 验证装备对象有效性

## 🎯 成功标志

### 完全成功的日志：
```
[DEBUG] === 开始执行外挂功能 ===
[DEBUG] 玩家编号: 1
[DEBUG] 功能类型: 1
[DEBUG] 开始添加装备功能，ID: 10101001, 数量: 1
[DEBUG] 装备ID 10101001 有效，名称: 金箍棒
[DEBUG] 创建第 1 个装备
[DEBUG] 尝试创建装备，ID: 10101001
[DEBUG] XMLSingle实例获取成功
[DEBUG] equipmentXML存在，开始创建装备
[DEBUG] 装备创建成功，类型: object
[DEBUG] 装备名称: 金箍棒
[DEBUG] 装备等级: 1
[DEBUG] 尝试添加装备到背包
[DEBUG] 背包存在，当前长度: 40
[DEBUG] 装备信息 - ID: 10101001, 名称: 金箍棒, 类型: weapon
[DEBUG] 通过MyFunction添加装备结果: true
[DEBUG] 第 1 个装备添加成功
[DEBUG] 装备添加完成，成功: 1/1
[DEBUG] 开始刷新UI
[DEBUG] 通过refresh方法刷新UI成功
[DEBUG] 刷新UI完成
[DEBUG] === 外挂功能执行结束 ===
```

### 游戏中的表现：
1. **背包中出现新装备**
2. **装备名称正确显示**
3. **装备属性正常**
4. **可以正常使用装备**

## 📝 测试记录

### 测试模板：
```
测试时间：[时间]
装备ID：[ID]
装备名称：[名称]
测试结果：[成功/失败]
错误信息：[具体错误]
游戏表现：[背包变化情况]
```

### 建议测试顺序：
1. ✅ 基础武器 (10101001)
2. ✅ 消耗品 (20001)
3. ✅ 防具 (10201001)
4. ✅ 饰品 (10301001)
5. ✅ 批量添加测试
6. ✅ 无效ID测试 (99999999)

## 🚀 下一步测试

装备添加功能成功后，可以继续测试：
1. **修改金币功能**
2. **修改等级功能**
3. **修改属性功能**
4. **其他高级功能**

---

**提示**：如果装备添加功能完全正常，说明外挂的核心机制已经工作，其他功能也应该能够正常使用。
