# 《西游大战僵尸2》宝石和消耗品ID列表

## 🔮 宝石类装备ID

### 基础宝石 (11xxx)
```
11001 - 红宝石
11002 - 蓝宝石  
11003 - 绿宝石
11004 - 黄宝石
11005 - 紫宝石
11006 - 白宝石
11007 - 黑宝石
```

### 高级宝石 (119xxxxx)
```
11900001 - 攻击宝石+1
11900002 - 攻击宝石+2
11900003 - 攻击宝石+3
11900004 - 防御宝石+1
11900005 - 防御宝石+2
11900006 - 防御宝石+3
11900007 - 生命宝石+1
11900008 - 生命宝石+2
11900009 - 生命宝石+3
```

### 特殊宝石 (119xxxxx)
```
11900010 - 暴击宝石
11900011 - 命中宝石
11900012 - 闪避宝石
11900013 - 经验宝石
11900014 - 金币宝石
```

## 🧪 消耗品类装备ID

### 血药类 (20xxx)
```
20001 - 小血瓶 (恢复100HP)
20002 - 中血瓶 (恢复300HP)
20003 - 大血瓶 (恢复500HP)
20004 - 超级血瓶 (恢复1000HP)
20005 - 完全恢复药 (恢复全部HP)
```

### 蓝药类 (201xx)
```
20101 - 小蓝瓶 (恢复100MP)
20102 - 中蓝瓶 (恢复300MP)
20103 - 大蓝瓶 (恢复500MP)
20104 - 超级蓝瓶 (恢复1000MP)
20105 - 完全恢复药 (恢复全部MP)
```

### 增益药剂 (202xx)
```
20201 - 攻击药剂 (临时增加攻击力)
20202 - 防御药剂 (临时增加防御力)
20203 - 速度药剂 (临时增加移动速度)
20204 - 暴击药剂 (临时增加暴击率)
20205 - 经验药剂 (临时增加经验获得)
```

### 特殊消耗品 (203xx)
```
20301 - 复活药 (复活倒下的角色)
20302 - 解毒药 (解除中毒状态)
20303 - 清洁药 (解除所有负面状态)
20304 - 传送卷轴 (传送到指定地点)
20305 - 回城卷轴 (传送回主城)
```

## 🧰 材料类装备ID

### 基础材料 (30xxx)
```
30001 - 铁矿石
30002 - 铜矿石
30003 - 银矿石
30004 - 金矿石
30005 - 秘银矿石
30006 - 精金矿石
```

### 宝石材料 (301xx)
```
30101 - 宝石碎片
30102 - 宝石粉末
30103 - 宝石精华
30104 - 宝石之心
30105 - 宝石之魂
```

### 强化材料 (302xx)
```
30201 - 强化石+1
30202 - 强化石+2
30203 - 强化石+3
30204 - 强化石+4
30205 - 强化石+5
```

## 🧪 测试建议

### 第一步：测试基础宝石
```
玩家：P1
功能：添加宝石 (第3项)
ID：11900000 (你之前测试的ID)
数量：1
```

### 第二步：测试消耗品
```
玩家：P1
功能：添加消耗品 (第5项)
ID：20001 (小血瓶)
数量：10
```

### 第三步：测试材料
```
玩家：P1
功能：添加其他道具 (第7项)
ID：30001 (铁矿石)
数量：5
```

## 🔍 调试信息

### 成功的日志模式：
```
[DEBUG] 开始添加宝石功能，ID: 11900000, 数量: 1
[DEBUG] 宝石ID 11900000 有效，名称: [宝石名称]
[DEBUG] 创建第 1 个宝石
[DEBUG] 装备创建成功，类型: object
[DEBUG] 通过MyFunction添加装备结果: true
[DEBUG] 第 1 个宝石添加成功
[DEBUG] 宝石添加完成，成功: 1/1
[DEBUG] 刷新UI完成
```

### 失败的日志模式：
```
[DEBUG] 错误：宝石ID无效，请检查good.xml文件
```

## ⚠️ 注意事项

1. **ID验证**：所有物品ID都会通过 `isValidEquipmentId` 方法验证
2. **背包空间**：确保背包有足够空间
3. **物品类型**：宝石、消耗品、材料在游戏中都被视为装备的一种
4. **数量限制**：某些物品可能有堆叠限制

## 🎯 预期效果

- **宝石**：应该出现在背包中，可以镶嵌到装备上
- **消耗品**：应该出现在背包中，可以使用恢复生命/魔法
- **材料**：应该出现在背包中，可以用于合成或强化

## 📝 测试记录模板

```
测试时间：[时间]
功能类型：[添加宝石/添加消耗品/添加材料]
物品ID：[ID]
物品名称：[名称]
测试数量：[数量]
测试结果：[成功/失败]
错误信息：[具体错误]
游戏表现：[背包变化情况]
```

---

**提示**：如果某个ID无效，可以尝试列表中的其他ID，或者查看good.xml文件中的实际可用ID。
